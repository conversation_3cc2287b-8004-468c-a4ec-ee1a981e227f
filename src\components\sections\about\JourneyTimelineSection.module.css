/* Journey Timeline Section Styles */

.timelineSection {
  /* Disable smooth scrolling on this section for better control */
  scroll-behavior: auto;
  /* Prevent momentum scrolling on iOS */
  -webkit-overflow-scrolling: auto;
  /* Ensure no overflow issues */
  overflow-x: hidden;
  /* Add scroll dampening for slower feel */
  scroll-snap-type: none;
}

.timelineContainer {
  /* Ensure no smooth scrolling behavior */
  scroll-behavior: auto;
  /* Add will-change for better performance */
  will-change: transform;
}

.timelinePath {
  filter: url(#timeline-glow);
  /* Smooth path animation */
  transition: stroke-dashoffset 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.avatarFallback {
  display: none;
}

/* Slower scroll animation styles */
.slowScrollContainer {
  /* Add CSS custom properties for smoother transitions */
  --scroll-speed: 0.4; /* Much slower scroll multiplier */
  --transition-duration: 1.2s; /* Longer transition duration */
  --ease-curve: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Smooth easing */
}

/* Override framer-motion default spring settings for slower animations */
.timelineContent {
  /* Custom scroll behavior for slower animations */
  transition: transform var(--transition-duration) var(--ease-curve);
}

/* Smooth but slower card animations */
.timelineCard {
  transition: all 0.6s var(--ease-curve);
  /* Add transform-origin for better scaling */
  transform-origin: center center;
}

.timelineCard:hover {
  transition: all 0.3s var(--ease-curve);
}

/* Add custom scroll dampening using CSS */
@media (prefers-reduced-motion: no-preference) {
  .timelineSection {
    /* Smooth scroll behavior override */
    scroll-behavior: smooth;
  }

  .timelineContainer {
    /* Add subtle scroll dampening */
    scroll-behavior: smooth;
  }
}
