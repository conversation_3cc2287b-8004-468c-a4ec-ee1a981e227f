/* Journey Timeline Section Styles */

.timelineSection {
  /* Allow natural browser scrolling */
  overflow-x: hidden;
}

.timelineContainer {
  /* Add will-change for better performance */
  will-change: transform;
}

.timelinePath {
  filter: url(#timeline-glow);
}

.avatarFallback {
  display: none;
}

/* Natural scroll animation styles */
.slowScrollContainer {
  /* Add CSS custom properties for smooth transitions */
  --transition-duration: 0.3s; /* Natural transition duration */
  --ease-curve: ease-out; /* Natural easing */
}

/* Natural card animations */
.timelineCard {
  transition: all 0.3s ease-out;
  /* Add transform-origin for better scaling */
  transform-origin: center center;
}

.timelineCard:hover {
  transition: all 0.2s ease-out;
}
